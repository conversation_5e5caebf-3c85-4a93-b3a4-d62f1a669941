<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ComposerSettings">
    <execution />
  </component>
  <component name="ExternalStorageConfigurationManager" enabled="true" />
  <component name="MavenProjectsManager">
    <option name="originalFiles">
      <list>
        <option value="$PROJECT_DIR$/spring-ai-mcp-client/pom.xml" />
        <option value="$PROJECT_DIR$/spring-ai-mcp-sse-server/pom.xml" />
        <option value="$PROJECT_DIR$/spring-ai-mcp-stdio-server/pom.xml" />
        <option value="$PROJECT_DIR$/spring-ai-mcp-mysql-server/pom.xml" />
      </list>
    </option>
  </component>
  <component name="ProjectRootManager" version="2" project-jdk-name="corretto-21" project-jdk-type="JavaSDK" />
</project>