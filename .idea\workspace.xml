<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7d178448-e1fa-4145-a713-bdf307bc1e01" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/spring-ai-mcp-client/src/main/java/com/yuncheng/mcp/OpenFileMaximized.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/spring-ai-mcp-mysql-server/src/main/resources/application-dev.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/spring-ai-mcp-mysql-server/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/spring-ai-mcp-client/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/spring-ai-mcp-client/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/spring-ai-mcp-client/src/main/java/com/yuncheng/mcp/McpClientApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/spring-ai-mcp-client/src/main/java/com/yuncheng/mcp/McpClientApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/spring-ai-mcp-client/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/spring-ai-mcp-client/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/spring-ai-mcp-sse-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/spring-ai-mcp-sse-server/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/spring-ai-mcp-stdio-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/spring-ai-mcp-stdio-server/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="KubernetesApiPersistence">{}</component>
  <component name="KubernetesApiProvider">{
  &quot;isMigrated&quot;: true
}</component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../soft/maven/apache-maven-3.8.6" />
        <option name="localRepository" value="D:\soft\maven\apache-maven-3.8.6\maven-repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\soft\maven\apache-maven-3.8.6\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2xDQQPC7BYk4KNE1MgHGBapKKtD" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
    <option name="showScratchesAndConsoles" value="false" />
  </component>
  <component name="PropertiesComponent"><![CDATA[{
  "keyToString": {
    "RequestMappingsPanelOrder0": "0",
    "RequestMappingsPanelOrder1": "1",
    "RequestMappingsPanelWidth0": "75",
    "RequestMappingsPanelWidth1": "75",
    "RunOnceActivity.OpenProjectViewOnStart": "true",
    "RunOnceActivity.ShowReadmeOnStart": "true",
    "RunOnceActivity.git.unshallow": "true",
    "RunOnceActivity.go.formatter.settings.were.checked": "true",
    "RunOnceActivity.go.migrated.go.modules.settings": "true",
    "Spring Boot.McpServerApplication (1).executor": "Run",
    "Spring Boot.McpServerApplication.executor": "Run",
    "Spring Boot.McplServerApplication.executor": "Run",
    "WebServerToolWindowFactoryState": "false",
    "git-widget-placeholder": "master",
    "go.import.settings.migrated": "true",
    "jdk.selected.JAVA_MODULE": "corretto-21",
    "kotlin-language-version-configured": "true",
    "last_opened_file_path": "D:/SpringAI Project",
    "node.js.detected.package.eslint": "true",
    "node.js.detected.package.tslint": "true",
    "node.js.selected.package.eslint": "(autodetect)",
    "node.js.selected.package.tslint": "(autodetect)",
    "nodejs_package_manager_path": "npm",
    "project.structure.last.edited": "模块",
    "project.structure.proportion": "0.0",
    "project.structure.side.proportion": "0.0",
    "run.configurations.included.in.services": "true",
    "settings.editor.selected.configurable": "advanced.settings",
    "spring.configuration.checksum": "7f99f0ce6b65b22e9c158eb3dd494cba",
    "vue.rearranger.settings.migration": "true"
  },
  "keyToStringList": {
    "DatabaseDriversLRU": [
      "mysql"
    ]
  }
}]]></component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-mysql-server\src\main\resources" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.example.mcp" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.McpClientApplication">
    <configuration default="true" type="GoApplicationRunConfiguration" factoryName="Go Application">
      <module name="spring-ai-mcp-demo" />
      <working_directory value="$PROJECT_DIR$" />
      <go_parameters value="-i" />
      <kind value="FILE" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="GoTestRunConfiguration" factoryName="Go Test">
      <module name="spring-ai-mcp-demo" />
      <working_directory value="$PROJECT_DIR$" />
      <go_parameters value="-i" />
      <kind value="DIRECTORY" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <framework value="gotest" />
      <method v="2" />
    </configuration>
    <configuration name="OpenFile" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.yuncheng.mcp.OpenFile" />
      <module name="spring-ai-mcp-client" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yuncheng.mcp.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OpenFileMaximized" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.yuncheng.mcp.OpenFileMaximized" />
      <module name="spring-ai-mcp-client" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yuncheng.mcp.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="McpClientApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="spring-ai-mcp-client" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yuncheng.mcp.McpClientApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="McpServerApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="spring-ai-mcp-sse-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yuncheng.mcp.McpServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="McpServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="spring-ai-mcp-stdio-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yuncheng.mcp.McpServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="McplServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="spring-ai-mcp-mysql-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.mcp.McplServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="Spring Boot.McplServerApplication" />
      <item itemvalue="Spring Boot.McpClientApplication" />
      <item itemvalue="Spring Boot.McpServerApplication" />
      <item itemvalue="Spring Boot.McpServerApplication (1)" />
      <item itemvalue="应用程序.OpenFile" />
      <item itemvalue="应用程序.OpenFileMaximized" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-125ca727e0f0-intellij.indexing.shared.core-IU-243.23654.153" />
        <option value="bundled-js-predefined-d6986cc7102b-822845ee3bb5-JavaScript-IU-243.23654.153" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="7d178448-e1fa-4145-a713-bdf307bc1e01" name="Changes" comment="" />
      <created>1747470147872</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747470147872</updated>
      <workItem from="1747470149307" duration="23643000" />
      <workItem from="1747620202158" duration="14547000" />
      <workItem from="1748395042464" duration="2999000" />
      <workItem from="1748398880335" duration="749000" />
      <workItem from="1748402509448" duration="45000" />
      <workItem from="1748404602653" duration="1015000" />
      <workItem from="1748425682729" duration="1166000" />
      <workItem from="1748481624159" duration="476000" />
      <workItem from="1748520670986" duration="44000" />
      <workItem from="1748692589747" duration="535000" />
      <workItem from="1754620437675" duration="544000" />
      <workItem from="1754621198469" duration="427000" />
      <workItem from="1754621639603" duration="556000" />
      <workItem from="1754622205356" duration="107000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
  <component name="XSLT-Support.FileAssociations.UIState">
    <expand />
    <select />
  </component>
</project>