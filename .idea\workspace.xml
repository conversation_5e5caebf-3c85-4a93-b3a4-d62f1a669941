<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="7d178448-e1fa-4145-a713-bdf307bc1e01" name="Changes" comment="">
      <change afterPath="$PROJECT_DIR$/spring-ai-mcp-client/src/main/java/com/yuncheng/mcp/OpenFileMaximized.java" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/spring-ai-mcp-mysql-server/src/main/resources/application-dev.yml" afterDir="false" />
      <change afterPath="$PROJECT_DIR$/spring-ai-mcp-mysql-server/src/main/resources/application.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/spring-ai-mcp-client/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/spring-ai-mcp-client/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/spring-ai-mcp-client/src/main/java/com/yuncheng/mcp/McpClientApplication.java" beforeDir="false" afterPath="$PROJECT_DIR$/spring-ai-mcp-client/src/main/java/com/yuncheng/mcp/McpClientApplication.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/spring-ai-mcp-client/src/main/resources/application-dev.yml" beforeDir="false" afterPath="$PROJECT_DIR$/spring-ai-mcp-client/src/main/resources/application-dev.yml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/spring-ai-mcp-sse-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/spring-ai-mcp-sse-server/pom.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/spring-ai-mcp-stdio-server/pom.xml" beforeDir="false" afterPath="$PROJECT_DIR$/spring-ai-mcp-stdio-server/pom.xml" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Class" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="MavenImportPreferences">
    <option name="generalSettings">
      <MavenGeneralSettings>
        <option name="customMavenHome" value="$PROJECT_DIR$/../soft/maven/apache-maven-3.8.6" />
        <option name="localRepository" value="D:\soft\maven\apache-maven-3.8.6\maven-repo" />
        <option name="mavenHomeTypeForPersistence" value="CUSTOM" />
        <option name="userSettingsFile" value="D:\soft\maven\apache-maven-3.8.6\conf\settings.xml" />
      </MavenGeneralSettings>
    </option>
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 3
}</component>
  <component name="ProjectId" id="2xDQQPC7BYk4KNE1MgHGBapKKtD" />
  <component name="ProjectViewState">
    <option name="showLibraryContents" value="true" />
    <option name="showScratchesAndConsoles" value="false" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;RequestMappingsPanelOrder0&quot;: &quot;0&quot;,
    &quot;RequestMappingsPanelOrder1&quot;: &quot;1&quot;,
    &quot;RequestMappingsPanelWidth0&quot;: &quot;75&quot;,
    &quot;RequestMappingsPanelWidth1&quot;: &quot;75&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.formatter.settings.were.checked&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.go.migrated.go.modules.settings&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;go.import.settings.migrated&quot;: &quot;true&quot;,
    &quot;jdk.selected.JAVA_MODULE&quot;: &quot;corretto-21&quot;,
    &quot;kotlin-language-version-configured&quot;: &quot;true&quot;,
    &quot;last_opened_file_path&quot;: &quot;D:/SpringAI Project&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;project.structure.last.edited&quot;: &quot;Modules&quot;,
    &quot;project.structure.proportion&quot;: &quot;0.0&quot;,
    &quot;project.structure.side.proportion&quot;: &quot;0.0&quot;,
    &quot;run.configurations.included.in.services&quot;: &quot;true&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;MavenSettings&quot;,
    &quot;spring.configuration.checksum&quot;: &quot;7f99f0ce6b65b22e9c158eb3dd494cba&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;DatabaseDriversLRU&quot;: [
      &quot;mysql&quot;
    ]
  }
}</component>
  <component name="ReactorSettings">
    <option name="notificationShown" value="true" />
  </component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-mysql-server\src\main\resources" />
    </key>
    <key name="MoveClassesOrPackagesDialog.RECENTS_KEY">
      <recent name="com.example.mcp" />
    </key>
  </component>
  <component name="RunDashboard">
    <option name="configurationTypes">
      <set>
        <option value="KtorApplicationConfigurationType" />
        <option value="MicronautRunConfigurationType" />
        <option value="QuarkusRunConfigurationType" />
        <option value="SpringBootApplicationConfigurationType" />
      </set>
    </option>
  </component>
  <component name="RunManager" selected="Spring Boot.McpClientApplication">
    <configuration default="true" type="GoApplicationRunConfiguration" factoryName="Go Application">
      <module name="spring-ai-mcp-demo" />
      <working_directory value="$PROJECT_DIR$" />
      <go_parameters value="-i" />
      <kind value="FILE" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <method v="2" />
    </configuration>
    <configuration default="true" type="GoTestRunConfiguration" factoryName="Go Test">
      <module name="spring-ai-mcp-demo" />
      <working_directory value="$PROJECT_DIR$" />
      <go_parameters value="-i" />
      <kind value="DIRECTORY" />
      <directory value="$PROJECT_DIR$" />
      <filePath value="$PROJECT_DIR$" />
      <framework value="gotest" />
      <method v="2" />
    </configuration>
    <configuration name="OpenFile" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.yuncheng.mcp.OpenFile" />
      <module name="spring-ai-mcp-client" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yuncheng.mcp.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="OpenFileMaximized" type="Application" factoryName="Application" temporary="true" nameIsGenerated="true">
      <option name="MAIN_CLASS_NAME" value="com.yuncheng.mcp.OpenFileMaximized" />
      <module name="spring-ai-mcp-client" />
      <extension name="coverage">
        <pattern>
          <option name="PATTERN" value="com.yuncheng.mcp.*" />
          <option name="ENABLED" value="true" />
        </pattern>
      </extension>
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="McpClientApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="spring-ai-mcp-client" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yuncheng.mcp.McpClientApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="McpServerApplication (1)" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="spring-ai-mcp-sse-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yuncheng.mcp.McpServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="McpServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="spring-ai-mcp-stdio-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.yuncheng.mcp.McpServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <configuration name="McplServerApplication" type="SpringBootApplicationConfigurationType" factoryName="Spring Boot" nameIsGenerated="true">
      <module name="spring-ai-mcp-mysql-server" />
      <option name="SPRING_BOOT_MAIN_CLASS" value="com.example.mcp.McplServerApplication" />
      <method v="2">
        <option name="Make" enabled="true" />
      </method>
    </configuration>
    <list>
      <item itemvalue="应用程序.OpenFileMaximized" />
      <item itemvalue="应用程序.OpenFile" />
      <item itemvalue="Spring Boot.McpClientApplication" />
      <item itemvalue="Spring Boot.McpServerApplication (1)" />
      <item itemvalue="Spring Boot.McpServerApplication" />
      <item itemvalue="Spring Boot.McplServerApplication" />
    </list>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-jdk-9823dce3aa75-125ca727e0f0-intellij.indexing.shared.core-IU-243.23654.153" />
        <option value="bundled-js-predefined-d6986cc7102b-822845ee3bb5-JavaScript-IU-243.23654.153" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="7d178448-e1fa-4145-a713-bdf307bc1e01" name="Changes" comment="" />
      <created>1747470147872</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1747470147872</updated>
      <workItem from="1747470149307" duration="23643000" />
      <workItem from="1747620202158" duration="14547000" />
      <workItem from="1748395042464" duration="2999000" />
      <workItem from="1748398880335" duration="749000" />
      <workItem from="1748402509448" duration="45000" />
      <workItem from="1748404602653" duration="1015000" />
      <workItem from="1748425682729" duration="1166000" />
      <workItem from="1748481624159" duration="476000" />
      <workItem from="1748520670986" duration="44000" />
      <workItem from="1748692589747" duration="535000" />
      <workItem from="1754620437675" duration="544000" />
      <workItem from="1754621198469" duration="427000" />
    </task>
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="VgoProject">
    <settings-migrated>true</settings-migrated>
  </component>
</project>