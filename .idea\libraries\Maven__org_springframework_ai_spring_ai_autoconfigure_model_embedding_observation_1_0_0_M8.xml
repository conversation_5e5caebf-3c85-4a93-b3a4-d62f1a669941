<component name="libraryTable">
  <library name="Maven: org.springframework.ai:spring-ai-autoconfigure-model-embedding-observation:1.0.0-M8">
    <CLASSES>
      <root url="jar://D:/soft/maven/apache-maven-3.8.6/maven-repo/org/springframework/ai/spring-ai-autoconfigure-model-embedding-observation/1.0.0-M8/spring-ai-autoconfigure-model-embedding-observation-1.0.0-M8.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://D:/soft/maven/apache-maven-3.8.6/maven-repo/org/springframework/ai/spring-ai-autoconfigure-model-embedding-observation/1.0.0-M8/spring-ai-autoconfigure-model-embedding-observation-1.0.0-M8-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://D:/soft/maven/apache-maven-3.8.6/maven-repo/org/springframework/ai/spring-ai-autoconfigure-model-embedding-observation/1.0.0-M8/spring-ai-autoconfigure-model-embedding-observation-1.0.0-M8-sources.jar!/" />
    </SOURCES>
  </library>
</component>