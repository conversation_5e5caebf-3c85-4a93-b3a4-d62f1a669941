package com.example.mcp;

import com.example.mcp.service.MySqlService;
import org.springframework.ai.tool.ToolCallbackProvider;
import org.springframework.ai.tool.method.MethodToolCallbackProvider;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.context.annotation.Bean;

@SpringBootApplication
public class McplServerApplication {

    public static void main(String[] args) {
        SpringApplication.run(McplServerApplication.class, args);
    }

    @Bean
    public ToolCallbackProvider mathTools(MySqlService mySqlService) {
        return MethodToolCallbackProvider.builder()
                .toolObjects(mySqlService).build();
    }
}
