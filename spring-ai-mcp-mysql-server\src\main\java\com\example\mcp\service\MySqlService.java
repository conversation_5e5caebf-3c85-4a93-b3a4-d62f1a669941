package com.example.mcp.service;

import org.springframework.ai.tool.annotation.Tool;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.jdbc.datasource.DriverManagerDataSource;
import org.springframework.stereotype.Service;

import javax.sql.DataSource;
import java.util.List;
import java.util.Map;

@Service
public class MySqlService {

    private final JdbcTemplate jdbcTemplate;
    private final DataSource dataSource;

    @Autowired
    public MySqlService(JdbcTemplate jdbcTemplate, DataSource dataSource) {
        this.jdbcTemplate = jdbcTemplate;
        this.dataSource = dataSource;
    }

    @Tool(description = "连接到MySQL数据库")
    public void connectToDatabase(String host, String user, String password, String database) {
        try {
            // 更新数据源配置
            DriverManagerDataSource dataSource = (DriverManagerDataSource) this.dataSource;
            dataSource.setUrl("jdbc:mysql://" + host + "/" + database);
            dataSource.setUsername(user);
            dataSource.setPassword(password);
            
            // 测试连接
            jdbcTemplate.execute("SELECT 1");
        } catch (Exception e) {
            throw new RuntimeException("数据库连接失败: " + e.getMessage());
        }
    }

    @Tool(description = "执行SQL查询，支持连表查询")
    public List<Map<String, Object>> executeQuery(String sql, List<Object> params) {
        try {
            return jdbcTemplate.queryForList(sql, params != null ? params.toArray() : new Object[0]);
        } catch (Exception e) {
            throw new RuntimeException("查询执行失败: " + e.getMessage());
        }
    }

    @Tool(description = "执行SQL更新操作（INSERT、UPDATE、DELETE）")
    public int executeUpdate(String sql, List<Object> params) {
        try {
            return jdbcTemplate.update(sql, params != null ? params.toArray() : new Object[0]);
        } catch (Exception e) {
            throw new RuntimeException("更新执行失败: " + e.getMessage());
        }
    }

    @Tool(description = "列出数据库中的所有表")
    public List<String> listTables() {
        try {
            return jdbcTemplate.queryForList("SHOW TABLES", String.class);
        } catch (Exception e) {
            throw new RuntimeException("获取表列表失败: " + e.getMessage());
        }
    }

    @Tool(description = "查看指定表的结构")
    public List<Map<String, Object>> describeTable(String tableName) {
        try {
            return jdbcTemplate.queryForList("DESCRIBE " + tableName);
        } catch (Exception e) {
            throw new RuntimeException("获取表结构失败: " + e.getMessage());
        }
    }
} 