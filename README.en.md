# spring-ai-mcp-demo

#### Description
本文介绍基于Spring AI如何实现MCP开发和调用。首先自定义了两个MCP Server，其中：一个是算术计算器MCP Server，并通过sdtio传输协议发布，另一个是天气预报MCP Server，通过sse传输协议发布。然后实现一个MCP Client，并调用阿里云qwen大模型完成整个 MCP 调用流程，并给出来整个示例的Java代码。

#### Software Architecture
Software architecture description

#### Installation

1.  xxxx
2.  xxxx
3.  xxxx

#### Instructions

1.  xxxx
2.  xxxx
3.  xxxx

#### Contribution

1.  Fork the repository
2.  Create Feat_xxx branch
3.  Commit your code
4.  Create Pull Request


#### Gitee Feature

1.  You can use Readme\_XXX.md to support different languages, such as Readme\_en.md, Readme\_zh.md
2.  Gitee blog [blog.gitee.com](https://blog.gitee.com)
3.  Explore open source project [https://gitee.com/explore](https://gitee.com/explore)
4.  The most valuable open source project [GVP](https://gitee.com/gvp)
5.  The manual of Gitee [https://gitee.com/help](https://gitee.com/help)
6.  The most popular members  [https://gitee.com/gitee-stars/](https://gitee.com/gitee-stars/)
