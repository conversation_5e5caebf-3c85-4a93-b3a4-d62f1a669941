2025-05-17 16:36:10 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication using Java 21.0.2 with PID 18448 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\classes started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 16:36:10 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 16:36:11 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-17 16:36:11 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-17 16:36:11 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-17 16:36:11 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.28]
2025-05-17 16:36:11 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-17 16:36:11 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 830 ms
2025-05-17 16:36:12 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 16:36:12 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-17 16:36:12 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-17 16:36:12 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-17 16:36:12 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-05-17 16:44:54 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication using Java 21.0.2 with PID 27544 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\classes started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 16:44:54 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 16:44:55 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-17 16:44:55 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-17 16:44:55 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-17 16:44:55 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.28]
2025-05-17 16:44:55 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-17 16:44:55 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 951 ms
2025-05-17 16:44:55 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 16:44:56 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-17 16:44:56 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-17 16:44:56 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-17 16:44:56 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-05-17 16:45:29 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 32252 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 16:45:29 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 16:45:30 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 16:45:30 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.495 seconds (process running for 3.182)
2025-05-17 16:45:30 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 16:46:18 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication using Java 21.0.2 with PID 26148 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\classes started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 16:46:18 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 16:46:19 [main] INFO  o.s.b.w.e.tomcat.TomcatWebServer - Tomcat initialized with port 8080 (http)
2025-05-17 16:46:19 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Initializing ProtocolHandler ["http-nio-8080"]
2025-05-17 16:46:19 [main] INFO  o.a.catalina.core.StandardService - Starting service [Tomcat]
2025-05-17 16:46:19 [main] INFO  o.a.catalina.core.StandardEngine - Starting Servlet engine: [Apache Tomcat/10.1.28]
2025-05-17 16:46:19 [main] INFO  o.a.c.c.C.[Tomcat].[localhost].[/] - Initializing Spring embedded WebApplicationContext
2025-05-17 16:46:19 [main] INFO  o.s.b.w.s.c.ServletWebServerApplicationContext - Root WebApplicationContext: initialization completed in 817 ms
2025-05-17 16:46:19 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 16:46:20 [main] INFO  o.a.coyote.http11.Http11NioProtocol - Starting ProtocolHandler ["http-nio-8080"]
2025-05-17 16:46:20 [main] WARN  o.s.b.w.s.c.AnnotationConfigServletWebServerApplicationContext - Exception encountered during context initialization - cancelling refresh attempt: org.springframework.context.ApplicationContextException: Failed to start bean 'webServerStartStop'
2025-05-17 16:46:20 [main] INFO  o.s.b.a.l.ConditionEvaluationReportLogger - 

Error starting ApplicationContext. To display the condition evaluation report re-run your application with 'debug' enabled.
2025-05-17 16:46:20 [main] ERROR o.s.b.d.LoggingFailureAnalysisReporter - 

***************************
APPLICATION FAILED TO START
***************************

Description:

Web server failed to start. Port 8080 was already in use.

Action:

Identify and stop the process that's listening on port 8080 or configure this application to listen on another port.

2025-05-17 16:47:17 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 30556 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 16:47:17 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 16:47:18 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 16:47:18 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.34 seconds (process running for 3.015)
2025-05-17 16:47:18 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 16:47:54 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 21020 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 16:47:54 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 16:47:56 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 16:47:56 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.49 seconds (process running for 3.144)
2025-05-17 16:47:56 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 16:53:01 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 33868 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 16:53:01 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 16:53:02 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 16:53:03 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.387 seconds (process running for 3.048)
2025-05-17 16:53:03 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 16:53:20 [boundedElastic-1] INFO  com.yuncheng.mcp.MathService - ===============multiply方法被调用: a=8, b=8
2025-05-17 22:21:16 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 23748 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 22:21:16 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 22:21:18 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 22:21:18 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.604 seconds (process running for 3.401)
2025-05-17 22:21:18 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 22:23:11 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 39332 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 22:23:11 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 22:23:13 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 22:23:13 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.672 seconds (process running for 3.405)
2025-05-17 22:23:13 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 22:26:27 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 37592 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 22:26:27 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 22:26:29 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 22:26:29 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.82 seconds (process running for 3.551)
2025-05-17 22:26:29 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 22:28:09 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 29996 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 22:28:09 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 22:28:10 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 22:28:11 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.531 seconds (process running for 3.233)
2025-05-17 22:28:11 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 22:36:03 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 33348 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 22:36:03 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 22:36:05 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 22:36:05 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.666 seconds (process running for 3.386)
2025-05-17 22:36:05 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 22:38:31 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 38356 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 22:38:31 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 22:38:33 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 22:38:33 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.606 seconds (process running for 3.341)
2025-05-17 22:38:33 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 22:39:10 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 15344 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 22:39:10 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 22:39:12 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 22:39:12 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.594 seconds (process running for 3.335)
2025-05-17 22:39:12 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 22:44:09 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 26460 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 22:44:09 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 22:44:11 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 22:44:11 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.925 seconds (process running for 3.72)
2025-05-17 22:44:11 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 22:46:49 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 15576 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 22:46:49 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 22:46:51 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 22:46:51 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.917 seconds (process running for 3.684)
2025-05-17 22:46:51 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
2025-05-17 22:49:43 [main] INFO  c.yuncheng.mcp.McpServerApplication - Starting McpServerApplication v1.0-SNAPSHOT using Java 21.0.2 with PID 41172 (C:\Users\<USER>\Desktop\spring-ai-mcp-demo\spring-ai-mcp-stdio-server\target\spring-ai-mcp-stdio-server.jar started by linmour in C:\Users\<USER>\Desktop\spring-ai-mcp-demo)
2025-05-17 22:49:43 [main] INFO  c.yuncheng.mcp.McpServerApplication - The following 1 profile is active: "dev"
2025-05-17 22:49:45 [main] INFO  o.s.a.a.m.s.MpcServerAutoConfiguration - Registered tools2 notification: true
2025-05-17 22:49:45 [main] INFO  c.yuncheng.mcp.McpServerApplication - Started McpServerApplication in 2.531 seconds (process running for 3.237)
2025-05-17 22:49:45 [main] INFO  c.yuncheng.mcp.McpServerApplication - ===============McpServerApplication服务启动成功
