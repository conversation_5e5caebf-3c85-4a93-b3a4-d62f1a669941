<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="Encoding">
    <file url="file://$PROJECT_DIR$/demo/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/demo/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/spring-ai-mcp-client/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/spring-ai-mcp-client/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/spring-ai-mcp-mysql-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/spring-ai-mcp-mysql-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/spring-ai-mcp-sse-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/spring-ai-mcp-sse-server/src/main/resources" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/spring-ai-mcp-stdio-server/src/main/java" charset="UTF-8" />
    <file url="file://$PROJECT_DIR$/spring-ai-mcp-stdio-server/src/main/resources" charset="UTF-8" />
  </component>
</project>