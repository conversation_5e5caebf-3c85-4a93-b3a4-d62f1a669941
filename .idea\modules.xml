<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="ProjectModuleManager">
    <modules>
      <module fileurl="file://$PROJECT_DIR$/spring-ai-mcp-client/spring-ai-mcp-client.iml" filepath="$PROJECT_DIR$/spring-ai-mcp-client/spring-ai-mcp-client.iml" />
      <module fileurl="file://$PROJECT_DIR$/.idea/spring-ai-mcp-demo.iml" filepath="$PROJECT_DIR$/.idea/spring-ai-mcp-demo.iml" />
      <module fileurl="file://$PROJECT_DIR$/spring-ai-mcp-mysql-server/spring-ai-mcp-mysql-server.iml" filepath="$PROJECT_DIR$/spring-ai-mcp-mysql-server/spring-ai-mcp-mysql-server.iml" />
      <module fileurl="file://$PROJECT_DIR$/spring-ai-mcp-sse-server/spring-ai-mcp-sse-server.iml" filepath="$PROJECT_DIR$/spring-ai-mcp-sse-server/spring-ai-mcp-sse-server.iml" />
      <module fileurl="file://$PROJECT_DIR$/spring-ai-mcp-stdio-server/spring-ai-mcp-stdio-server.iml" filepath="$PROJECT_DIR$/spring-ai-mcp-stdio-server/spring-ai-mcp-stdio-server.iml" />
    </modules>
  </component>
</project>