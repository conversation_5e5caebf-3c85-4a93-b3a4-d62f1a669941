﻿server:
  port: 8081
#数据库连接所需配置
mysql:
  host: localhost
  user: root
  password: cheng128
  port: 3306
  database: iot
spring:
  ai:
    mcp:
      client:
        type: ASYNC
#        sse:
#          connections:
#            server1:
#              url: http://localhost:9090
        stdio:
          connections:
            server2:
              command: cmd
              args: [
               "/c",
               "npx",
               "-y",
               "@smithery/cli@latest",
               "run",
               "@f4ww4z/mcp-mysql-server",
               "--key",
               "8f46c4c1-fd31-4d20-9273-dd23feb87d25",

             ]

            server3:
              command: cmd
              args: [
                   "/c",
                   "npx",
                   "-y",
                   "@smithery/cli@latest",
                   "run",
                   "@smithery-ai/filesystem",
                   "--key",
                   "8f46c4c1-fd31-4d20-9273-dd23feb87d25",
                   "--profile",
                   "round-aardvark-X1nhF8"
                 ]

            server4:
              command: cmd
              args: [
                "/c",
                "npx",
                "-y",
                "@smithery/cli@latest",
                "run",
                "@negokaz/excel-mcp-server",
                "--key",
                "8f46c4c1-fd31-4d20-9273-dd23feb87d25"

              ]
#            server5:
#              command: uv
#              args: [
#                "--directory",
#                "C:/Users/<USER>/Desktop/mysql_mcp_server_pro/src",  # 这里需要替换为你的项目路径
#                "run",
#                "server.py",
#                "--stdio"
#              ]
#              env: {
#                "MYSQL_HOST": "*************",
#                "MYSQL_PORT": "3306",
#                "MYSQL_USER": "root",
#                "MYSQL_PASSWORD": "cheng128",
#                "MYSQL_DATABASE": "iot",
#                "MYSQL_ROLE": "admin"
#              }

    #            server6:
#              command: java
#              args:
#                - -jar
#                - C:/Users/<USER>/Desktop/spring-ai-mcp-demo/spring-ai-mcp-mysql-server/target/spring-ai-mcp-mysql-server.jar

    dashscope:
      api-key: sk-cb2164a825c84f6f80304777771faf32
      chat:
        options:
          #模型名称： qwen-plus  deepseek-v3  deepseek-r1
          model: qwen-plus

