# 测试环境配置文件
server:
  port: 8084

spring:
  # 数据源配置 - 测试环境
  datasource:
    url: *******************************************************************************************************************************
    username: root
    password: 1234
    # 连接池配置
    hikari:
      maximum-pool-size: 5
      minimum-idle: 2
      connection-timeout: 30000
      idle-timeout: 300000
      max-lifetime: 900000
    dbcp2:
      driver: com.mysql.cj.jdbc.Driver

  # Spring AI MCP 服务器配置
  ai:
    mcp:
      server:
        enabled: true
        name: spring-ai-mcp-mysql-server-test
        version: 1.0.0-test
        transport:
          type: stdio

# 日志配置
logging:
  level:
    com.example: DEBUG
    org.springframework.ai: DEBUG
    org.springframework.jdbc: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
