<component name="libraryTable">
  <library name="Maven: io.rest-assured:rest-assured-common:5.4.0">
    <CLASSES>
      <root url="jar://D:/soft/maven/apache-maven-3.8.6/maven-repo/io/rest-assured/rest-assured-common/5.4.0/rest-assured-common-5.4.0.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://D:/soft/maven/apache-maven-3.8.6/maven-repo/io/rest-assured/rest-assured-common/5.4.0/rest-assured-common-5.4.0-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://D:/soft/maven/apache-maven-3.8.6/maven-repo/io/rest-assured/rest-assured-common/5.4.0/rest-assured-common-5.4.0-sources.jar!/" />
    </SOURCES>
  </library>
</component>