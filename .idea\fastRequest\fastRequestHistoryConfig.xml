<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="fastRequestHistoryConfig">
    <option name="historyList">
      <list>
        <ResponseHistory>
          <option name="className" value="com.yuncheng.mcp.ChatController" />
          <option name="code" value="200" />
          <option name="enableEnv" value="dev" />
          <option name="enableProject" value="spring-ai-mcp-client" />
          <option name="method" value="generate" />
          <option name="methodType" value="GET" />
          <option name="paramGroup">
            <ParamGroupCollection>
              <option name="className" value="com.yuncheng.mcp.ChatController" />
              <option name="method" value="generate" />
              <option name="methodDescription" value="New Request" />
              <option name="multipartKeyValueListJson" value="[]" />
              <option name="originUrl" value="/ai/chat" />
              <option name="pathParamsKeyValueListJson" value="[]" />
              <option name="returnDocument" value="&quot;No comment,Type =String&quot;" />
              <option name="url" value="/ai/chat" />
              <option name="urlEncodedKeyValueListJson" value="[]" />
              <option name="urlParamsKeyValueListJson" value="[{&quot;comment&quot;:&quot;&quot;,&quot;customFlag&quot;:2,&quot;enabled&quot;:true,&quot;key&quot;:&quot;message&quot;,&quot;type&quot;:&quot;String&quot;,&quot;value&quot;:&quot;你是什么大模型&quot;}]" />
              <option name="urlParamsKeyValueListText" value="message=你是什么大模型" />
            </ParamGroupCollection>
          </option>
          <option name="requestInfo" value="Request Url: http://localhost:8081/ai/chat?message=%E4%BD%A0%E6%98%AF%E4%BB%80%E4%B9%88%E5%A4%A7%E6%A8%A1%E5%9E%8B&#13;&#10;Request Headers: &#13;&#10;    Accept: application/json,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8&#13;&#10;    User-Agent: Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/75.0.3770.142 Safari/537.36 Hutool&#13;&#10;    Accept-Encoding: gzip, deflate&#13;&#10;    Accept-Language: zh-CN,zh;q=0.8&#13;&#10;Request Body: &#13;&#10;    null&#13;&#10;" />
          <option name="response" value="我是通义千问，英文名Qwen，是阿里巴巴集团旗下的通义实验室自主研发的超大规模语言模型。我能够回答问题、创作文字，比如写故事、写公文、写邮件、写剧本、逻辑推理、编程等等，还能表达观点，玩游戏等。如果你有任何问题或需要帮助，欢迎随时告诉我！" />
          <option name="url" value="/ai/chat" />
        </ResponseHistory>
      </list>
    </option>
  </component>
</project>