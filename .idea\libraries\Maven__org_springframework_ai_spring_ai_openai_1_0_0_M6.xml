<component name="libraryTable">
  <library name="Maven: org.springframework.ai:spring-ai-openai:1.0.0-M6">
    <CLASSES>
      <root url="jar://D:/soft/maven/apache-maven-3.8.6/maven-repo/org/springframework/ai/spring-ai-openai/1.0.0-M6/spring-ai-openai-1.0.0-M6.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://D:/soft/maven/apache-maven-3.8.6/maven-repo/org/springframework/ai/spring-ai-openai/1.0.0-M6/spring-ai-openai-1.0.0-M6-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://D:/soft/maven/apache-maven-3.8.6/maven-repo/org/springframework/ai/spring-ai-openai/1.0.0-M6/spring-ai-openai-1.0.0-M6-sources.jar!/" />
    </SOURCES>
  </library>
</component>