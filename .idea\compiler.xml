<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="CompilerConfiguration">
    <annotationProcessing>
      <profile name="Maven default annotation processors profile" enabled="true">
        <sourceOutputDir name="target/generated-sources/annotations" />
        <sourceTestOutputDir name="target/generated-test-sources/test-annotations" />
        <outputRelativeToContentRoot value="true" />
        <module name="spring-ai-mcp-client" />
        <module name="spring-ai-mcp-mysql-server" />
        <module name="spring-ai-mcp-sse-server" />
        <module name="spring-ai-mcp-stdio-server" />
        <module name="demo" />
      </profile>
    </annotationProcessing>
    <bytecodeTargetLevel>
      <module name="demo" target="1.8" />
    </bytecodeTargetLevel>
  </component>
  <component name="JavacSettings">
    <option name="ADDITIONAL_OPTIONS_OVERRIDE">
      <module name="spring-ai-mcp-client" options="-parameters" />
      <module name="spring-ai-mcp-mysql-server" options="-parameters" />
      <module name="spring-ai-mcp-sse-server" options="-parameters" />
      <module name="spring-ai-mcp-stdio-server" options="-parameters" />
    </option>
  </component>
</project>