<component name="libraryTable">
  <library name="Maven: org.springframework:spring-context-support:6.1.12">
    <CLASSES>
      <root url="jar://D:/soft/maven/apache-maven-3.8.6/maven-repo/org/springframework/spring-context-support/6.1.12/spring-context-support-6.1.12.jar!/" />
    </CLASSES>
    <JAVADOC>
      <root url="jar://D:/soft/maven/apache-maven-3.8.6/maven-repo/org/springframework/spring-context-support/6.1.12/spring-context-support-6.1.12-javadoc.jar!/" />
    </JAVADOC>
    <SOURCES>
      <root url="jar://D:/soft/maven/apache-maven-3.8.6/maven-repo/org/springframework/spring-context-support/6.1.12/spring-context-support-6.1.12-sources.jar!/" />
    </SOURCES>
  </library>
</component>