package com.yuncheng.mcp;

import java.awt.*;
import java.awt.event.KeyEvent;
import java.io.File;
import java.io.IOException;

public class OpenFileMaximized {
    public static void main(String[] args) {
        // 设置要打开的 Markdown 文件路径
        String filePath = "C:/Users/<USER>/Desktop/5.5可以用/设备信息报表.md";

        // 获取文件对象
        File file = new File(filePath);

        // 检查文件是否存在
        if (!file.exists()) {
            System.out.println("文件不存在！");
            return;
        }

        try {
            // 检查 Desktop 是否支持打开文件
            if (Desktop.isDesktopSupported() && Desktop.getDesktop().isSupported(Desktop.Action.OPEN)) {
                // 打开文件
                Desktop.getDesktop().open(file);
                System.out.println("文件已通过默认程序打开！");

                // 等待文件打开并获取焦点
                Thread.sleep(1000); // 等待1秒，确保文件已打开

                // 使用 Robot 模拟按键操作来最大化窗口
                maximizeWindowWindows();
            } else {
                System.out.println("当前环境不支持通过 Desktop 打开文件！");
            }
        } catch (IOException | InterruptedException e) {
            e.printStackTrace();
            System.out.println("打开文件时发生错误！");
        }
    }

    // Windows 系统最大化窗口
    private static void maximizeWindowWindows() {
        try {
            Robot robot = new Robot();

            // 模拟按下 Alt + Space 组合键
            robot.keyPress(KeyEvent.VK_ALT);
            robot.keyPress(KeyEvent.VK_SPACE);
            robot.delay(200); // 短暂延迟，确保菜单弹出
            robot.keyRelease(KeyEvent.VK_SPACE);
            robot.keyRelease(KeyEvent.VK_ALT);

            // 模拟按下 X 键（最大化窗口）
            robot.keyPress(KeyEvent.VK_X);
            robot.delay(200); // 短暂延迟，确保窗口最大化
//            robot.keyRelease(KeyEvent.VK_X);
        } catch (AWTException e) {
            e.printStackTrace();
        }
    }
}