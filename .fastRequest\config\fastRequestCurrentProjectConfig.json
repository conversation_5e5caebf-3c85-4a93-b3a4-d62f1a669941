{"dataList": [{"hostGroup": [{"env": "dev", "url": "http://localhost:8081"}], "name": "spring-ai-mcp-client"}], "envList": ["dev"], "headerList": [], "maxDescriptionLength": -1, "postScript": "", "preScript": "", "projectList": ["spring-ai-mcp-client"], "syncModel": {"branch": "master", "domain": "https://github.com", "enabled": false, "namingPolicy": "byDoc", "owner": "", "repo": "", "repoUrl": "", "syncAfterRun": false, "token": "", "type": "github"}, "urlEncodedKeyValueList": [], "urlParamsKeyValueList": [], "urlSuffix": ""}