<?xml version="1.0" encoding="UTF-8"?>
<!-- Spring AI MCP STDIO 服务器模块 - 提供基于标准输入输出的 MCP 服务器功能 -->
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 继承 Spring Boot 父项目，自动管理依赖版本和插件配置 -->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.3</version>
        <relativePath/>
    </parent>

    <!-- 项目基本信息 -->
    <groupId>org.example</groupId>
    <artifactId>spring-ai-mcp-stdio-server</artifactId>
    <version>1.0-SNAPSHOT</version>

    <!-- 项目属性配置 -->
    <properties>
        <!-- Java 编译器源码版本设置为 21 -->
        <maven.compiler.source>21</maven.compiler.source>
        <!-- Java 编译器目标版本设置为 21 -->
        <maven.compiler.target>21</maven.compiler.target>
        <!-- 项目源码编码格式设置为 UTF-8 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- Spring AI 框架版本号 -->
        <spring-ai.version>1.0.0-M6</spring-ai.version>
        <!-- 阿里云 Spring AI 扩展版本号 -->
        <spring-ai-alibaba.version>1.0.0-M5.1</spring-ai-alibaba.version>
    </properties>

    <!-- 依赖管理 - 统一管理项目中使用的依赖版本 -->
    <dependencyManagement>
        <dependencies>
            <!-- Spring AI BOM (Bill of Materials) - 统一管理 Spring AI 相关依赖的版本 -->
            <dependency>
                <groupId>org.springframework.ai</groupId>
                <artifactId>spring-ai-bom</artifactId>
                <version>${spring-ai.version}</version>
                <type>pom</type>
                <scope>import</scope>
            </dependency>
        </dependencies>
    </dependencyManagement>

    <!-- 项目依赖配置 -->
    <dependencies>
        <!-- Spring Boot Web 启动器 - 提供 Web 应用基础功能，包括 Spring MVC、内嵌 Tomcat 等 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring AI MCP 服务器 WebFlux 启动器 - 提供基于 WebFlux 的响应式 MCP 服务器功能 -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-mcp-server-webflux-spring-boot-starter</artifactId>
        </dependency>

    </dependencies>

    <!-- Maven 仓库配置 - 指定依赖下载的远程仓库 -->
    <repositories>
        <!-- Central Portal 快照仓库 - 用于下载最新的快照版本依赖 -->
        <repository>
            <name>Central Portal Snapshots</name>
            <id>central-portal-snapshots</id>
            <url>https://central.sonatype.com/repository/maven-snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        <!-- Spring 里程碑版本仓库 - 用于下载 Spring 框架的里程碑版本 -->
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <!-- Spring 快照版本仓库 - 用于下载 Spring 框架的最新快照版本 -->
        <repository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
    </repositories>

    <!-- 构建配置 -->
    <build>
        <!-- 设置最终生成的 JAR 文件名为项目的 artifactId -->
        <finalName>${project.artifactId}</finalName>

        <!-- 构建插件配置 -->
        <plugins>
            <!-- Spring Boot Maven 插件 - 用于打包可执行的 JAR 文件和运行应用 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>