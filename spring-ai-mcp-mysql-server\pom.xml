<?xml version="1.0" encoding="UTF-8"?>
<!-- Spring AI MCP MySQL 服务器模块 - 提供基于 MySQL 数据库的 MCP 服务器功能 -->
<project xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xmlns="http://maven.apache.org/POM/4.0.0"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <modelVersion>4.0.0</modelVersion>

    <!-- 继承 Spring Boot 父项目，自动管理依赖版本和插件配置 -->
    <parent>
        <groupId>org.springframework.boot</groupId>
        <artifactId>spring-boot-starter-parent</artifactId>
        <version>3.3.3</version>
        <relativePath/>
    </parent>

    <!-- 项目基本信息 -->
    <groupId>com.example</groupId>
    <artifactId>spring-ai-mcp-mysql-server</artifactId>
    <version>1.0-SNAPSHOT</version>

    <!-- 项目属性配置 -->
    <properties>
        <!-- Java 编译器源码版本设置为 21 -->
        <maven.compiler.source>21</maven.compiler.source>
        <!-- Java 编译器目标版本设置为 21 -->
        <maven.compiler.target>21</maven.compiler.target>
        <!-- 项目源码编码格式设置为 UTF-8 -->
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <!-- Spring AI 框架版本号 -->
        <spring-ai.version>1.0.0-M2</spring-ai.version>
    </properties>

    <!-- 项目依赖配置 -->
    <dependencies>
        <!-- Spring Boot Web 启动器 - 提供 Web 应用基础功能，包括 Spring MVC、内嵌 Tomcat 等 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-web</artifactId>
        </dependency>

        <!-- Spring Boot JDBC 启动器 - 提供数据库连接和 JDBC 操作支持 -->
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-jdbc</artifactId>
        </dependency>

        <!-- MySQL 数据库连接驱动 - 用于连接和操作 MySQL 数据库 -->
        <dependency>
            <groupId>com.mysql</groupId>
            <artifactId>mysql-connector-j</artifactId>
            <version>8.3.0</version>
        </dependency>

        <!-- Spring AI 核心库 - 提供 Spring AI 框架的核心功能和 API -->
        <dependency>
            <groupId>org.springframework.ai</groupId>
            <artifactId>spring-ai-core</artifactId>
            <version>1.0.0-M6</version>
            <scope>compile</scope>
        </dependency>
    </dependencies>

    <!-- Maven 仓库配置 - 指定依赖下载的远程仓库 -->
    <repositories>
        <!-- Central Portal 快照仓库 - 用于下载最新的快照版本依赖 -->
        <repository>
            <name>Central Portal Snapshots</name>
            <id>central-portal-snapshots</id>
            <url>https://central.sonatype.com/repository/maven-snapshots/</url>
            <releases>
                <enabled>false</enabled>
            </releases>
            <snapshots>
                <enabled>true</enabled>
            </snapshots>
        </repository>

        <!-- Spring 里程碑版本仓库 - 用于下载 Spring 框架的里程碑版本 -->
        <repository>
            <id>spring-milestones</id>
            <name>Spring Milestones</name>
            <url>https://repo.spring.io/milestone</url>
            <snapshots>
                <enabled>false</enabled>
            </snapshots>
        </repository>

        <!-- Spring 快照版本仓库 - 用于下载 Spring 框架的最新快照版本 -->
        <repository>
            <id>spring-snapshots</id>
            <name>Spring Snapshots</name>
            <url>https://repo.spring.io/snapshot</url>
            <releases>
                <enabled>false</enabled>
            </releases>
        </repository>
    </repositories>

    <!-- 构建配置 -->
    <build>
        <!-- 设置最终生成的 JAR 文件名为项目的 artifactId -->
        <finalName>${project.artifactId}</finalName>

        <!-- 构建插件配置 -->
        <plugins>
            <!-- Spring Boot Maven 插件 - 用于打包可执行的 JAR 文件和运行应用 -->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>

</project>